import styled from "styled-components";


export const DivForm = styled.div`

        border-left : 20px solid rgba(228, 17, 101, 1);
        background: rgba(245, 247, 250, 1);
        box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.25);
        border-radius: 8px;
        width : 544px;
        height : 772px;
        display: grid;
        grid-template-columns: 100%;
        align-items: left;
        padding: 20px;
        gap: 10px;
        position: absolute;
        left: 3.37%;
        right: 0%;
        top: 0%;
        bottom: 0%;
      
   `;
 export const Title = styled.div`
   padding-bottom: 35%;
 `;

export const DivUser = styled.div`
    padding-bottom: 5%;
`;

export const DivCenter = styled.div`
  
    display: grid;
    grid-template-columns: 53% 53% ;
    align-items: center;
    padding-bottom: 5%;

   
`;

export const TitleEmail = styled.p`
    margin-left : 0px;
    margin-right: 5px;
`;

export const TitleLevel = styled.p`
    margin-left : 0px;
    margin-right: 5px;
 
`;

export const DivStatus = styled.div`

`;

export const DivEmail = styled.div`

width: 100%;


`;
export const DivLevel = styled.div`

width: 100%;

`;

export const InputUser = styled.input`
    width: 93%;
    border:2px solid #D3D3D3;
    border-radius:7px;
    font-size:20px;
    padding:5px; 
    border-radius:5px;
    font-size: medium;

`;
export const InputEmail = styled.input`
    width: 85%;
    border:2px solid #D3D3D3;
    border-radius:7px;
    font-size:20px;
    padding:5px; 
    border-radius:5px;
    font-size: medium;
`;

export const SelectStatus = styled.select`

    width: 95%;
    padding: 1%;
    border:2px solid #D3D3D3;
    border-radius:7px;
    font-size:20px;
    padding:5px; 
    border-radius:5px;
    font-size: medium;  
`;

export const SelectLevel = styled.select`

    width: 90%;
    border:2px solid #D3D3D3;
    border-radius:7px;
    font-size:20px;
    padding:5px; 
    border-radius:5px;
    font-size: medium;
`;

export const Button1 = styled.div`
    padding-top:30%;
    padding-bottom: 2%;
    margin-left:100px;
  
`;
export const Button2 = styled.div`
    margin-left : 100px;
    
`;
import { DAYS } from "./conts";

export const getDaysInMonth = (date) => {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
};

export const range = (end) => {
  const { result } = Array.from({ length: end }).reduce(
    ({ result, current }) => ({
      result: [...result, current],
      current: current + 1
    }),
    { result: [], current: 1 }
  );
  return result;
};

export const sortDays = (date) => {
  const dayIndex = new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  const sortedDays = [...DAYS.slice(dayIndex), ...DAYS.slice(0, dayIndex)];
  return sortedDays;
};

export const datesAreOnSameDay = (first, second) =>
  first.getFullYear() === second.getFullYear() &&
  first.getMonth() === second.getMonth() &&
  first.getDate() === second.getDate();

export const getMonthYear = (date) => {
  const d = date.toDateString().split(" ");
  //console.log(currentDate)
  return `${d[1]} ${d[3]}`;
};

export const nextMonth = (date, cb) => {
  const mon = date.getMonth();
  if (mon < 11) {
    date.setMonth(mon + 1);
  } else {
    date.setMonth(0);
    date.setFullYear(date.getFullYear() + 1);
  }
  cb(new Date(date));
};

export const prevMonth = (date, cb) => {
  const mon = date.getMonth();
  if (mon > 0) {
    date.setMonth(mon - 1);
  } else {
    date.setMonth(11);
    date.setFullYear(date.getFullYear() - 1);
  }
  cb(new Date(date));
};


export const getDarkColor = () => {
  var color = "#";
  for (var i = 0; i < 6; i++) {
    color += Math.floor(Math.random() * 10);
  }
  return color;
};


// retorna todos os meses de um mês
export const getSortedDays = (date) => {
  const daysInMonth = range(getDaysInMonth(date));
  const index = new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  const r=[...Array(index === 0 ? 6 : index - 1), ...daysInMonth]
  var a=[]
  for(let i=1;i<=42-r.length;i++){
    a=[...a,i]
  }
  return [...Array(index === 0 ? 6 : index - 1), ...daysInMonth,...a];
};


export const getSortedDaysDate = (date) => {
/*  const month= ((index < 27) && (day < 28)) ? currentDate.getMonth() :
        day > 20 ?  currentDate.getMonth():
        currentDate.getMonth() === 11 ? 0 : currentDate.getMonth() +1;
  const isCurrentMonth= month===currentDate.getMonth();
  const year= (month===currentDate.getMonth()) ? currentDate.getFullYear():
    month===0? currentDate.getFullYear()+1:
    month===11 ? currentDate.getFullYear()-1 : currentDate.getFullYear();
  const dayOfWeek=new Date(currentDate.getFullYear(),month,day).getDay()
*/
  
  const daysInMonth = range(getDaysInMonth(date));
  const daysInMonth1 = getDaysInMonth(date);
  const prevMonth = date.getMonth() === 0 ? 12 : date.getMonth();
  const month = date.getMonth()+1; 
  const nextMonth = date.getMonth() === 11 ? 1 : date.getMonth()+2;
  const prevYear = prevMonth===12 ? date.getFullYear()-1: date.getFullYear();
  const year = date.getFullYear();
  const nextYear = nextMonth===1 ? date.getFullYear()+1: date.getFullYear();
  const index = new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  let b=[];
  const daysLastMonth= getDaysInMonth(new Date(prevYear,prevMonth-1,1));
  const aux =  index === 0 ? 5 : index-2;
  for(let i=daysLastMonth-aux;i<=daysLastMonth;i++){
    b=[...b, `${prevYear}-${prevMonth}-${i}`];
  }
  
  for(let i=1;i<=daysInMonth1;i++){
    b=[...b, `${year}-${month}-${i}`];
  }

 
  for(let i=1;b.length<42;i++){
    b=[...b,`${nextYear}-${nextMonth}-${i}`]
  }
 
  return b;
};


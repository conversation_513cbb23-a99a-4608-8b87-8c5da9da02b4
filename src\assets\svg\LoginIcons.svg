<svg width="2010" height="630" viewBox="0 0 810 630" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_601_3962)">
<g filter="url(#filter0_d_601_3962)">
<path d="M168.02 414.31C131.428 378.288 115.973 340.708 124.502 308.494C135.446 267.154 184.066 238.57 257.896 230.073L258.169 232.444C185.367 240.823 137.488 268.765 126.809 309.104C118.509 340.455 133.739 377.214 169.694 412.61L168.02 414.31Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter1_d_601_3962)">
<path d="M521.068 542.177C475.399 542.177 425.772 535.39 376.478 522.34C315.52 506.202 259.627 481.843 214.844 451.897L216.17 449.913C260.738 479.715 316.383 503.962 377.089 520.033C431.948 534.557 487.219 541.286 536.905 539.51L536.992 541.895C531.744 542.083 526.436 542.177 521.068 542.177Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter2_d_601_3962)">
<path d="M625.838 527.169L625.074 524.908C665.918 511.099 691.465 488.853 698.952 460.573C708.469 424.624 687.793 382.907 640.733 343.106C593.433 303.104 525.225 269.912 448.672 249.646L449.283 247.338C526.17 267.694 594.71 301.058 642.274 341.284C690.077 381.713 711.025 424.294 701.259 461.184C693.558 490.274 667.478 513.091 625.838 527.169Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter3_d_601_3962)">
<path d="M250.789 543.174C161.053 503.375 91.904 451.741 56.082 397.781L58.07 396.461C93.6444 450.047 162.43 501.375 251.756 540.993L250.789 543.174Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter4_d_601_3962)">
<path d="M566.297 612.123C557.041 612.123 547.627 611.916 538.055 611.503L538.157 609.119C609.59 612.212 672.617 603.663 720.436 584.393L721.328 586.607C679.615 603.415 626.447 612.123 566.297 612.123Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter5_d_601_3962)">
<path d="M514.229 232.213C499.397 227.384 484.141 222.888 468.885 218.849C381.093 195.607 294.793 187.355 219.31 194.99L219.07 192.615C294.834 184.955 381.43 193.227 469.496 216.542C484.795 220.592 500.094 225.101 514.968 229.944L514.229 232.213Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter6_d_601_3962)">
<path d="M111.938 417.89C75.346 381.868 59.8914 344.288 68.4196 312.074C79.364 270.733 127.984 242.15 201.814 233.653L202.087 236.024C129.285 244.402 81.4061 272.344 70.7269 312.684C62.4271 344.035 77.6574 380.794 113.612 416.189L111.938 417.89Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter7_d_601_3962)">
<path d="M464.986 545.756C419.317 545.756 369.69 538.97 320.396 525.92C259.438 509.782 203.545 485.423 158.762 455.477L160.088 453.493C204.656 483.295 260.301 507.542 321.007 523.612C375.866 538.137 431.137 544.866 480.823 543.09L480.91 545.474C475.662 545.663 470.354 545.757 464.986 545.756Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter8_d_601_3962)">
<path d="M569.752 530.749L568.988 528.488C609.832 514.678 635.379 492.432 642.866 464.153C652.383 428.204 631.707 386.486 584.647 346.686C537.347 306.684 469.139 273.492 392.586 253.225L393.197 250.918C470.084 271.273 538.624 304.637 586.188 344.864C633.991 385.293 654.94 427.874 645.173 464.764C637.472 493.854 611.392 516.671 569.752 530.749Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter9_d_601_3962)">
<path d="M194.707 546.754C104.971 506.955 35.822 455.32 0 401.361L1.98799 400.041C37.5624 453.627 106.348 504.955 195.674 544.573L194.707 546.754Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter10_d_601_3962)">
<path d="M510.214 615.703C500.959 615.703 491.545 615.496 481.973 615.083L482.075 612.699C553.508 615.791 616.535 607.243 664.354 587.972L665.246 590.186C623.533 606.995 570.365 615.703 510.214 615.703Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter11_d_601_3962)">
<path d="M458.147 235.793C443.315 230.964 428.059 226.468 412.803 222.429C325.011 199.187 238.711 190.935 163.228 198.57L162.988 196.195C238.752 188.535 325.348 196.807 413.414 220.121C428.713 224.172 444.012 228.681 458.886 233.524L458.147 235.793Z" fill="#F0F0F0"/>
</g>
<g filter="url(#filter12_d_601_3962)">
<path d="M40.6779 353.969C57.8277 334.74 85.1844 323.021 109.976 330.037C87.1047 346.222 70.6958 369.975 63.6521 397.094C60.9736 407.563 59.0845 419.485 50.3784 425.886C44.9613 429.869 37.7835 430.82 31.1215 429.913C24.4593 429.006 18.16 426.415 11.9456 423.848L10.2734 423.993C15.0812 398.68 23.5282 373.197 40.6779 353.969Z" fill="#F2F2F2"/>
</g>
<g filter="url(#filter13_d_601_3962)">
<path d="M109.898 330.572C89.1703 335.59 70.5513 347.012 56.6851 363.215C53.6381 366.65 51.1516 370.544 49.3172 374.754C47.6627 378.912 47.1519 383.437 47.8379 387.86C48.3947 391.996 49.4433 396.163 48.9849 400.362C48.7183 402.509 48.0011 404.574 46.8802 406.424C45.7594 408.273 44.2602 409.865 42.481 411.095C38.1275 414.274 32.8914 415.699 27.7225 416.914C21.9834 418.264 16.0009 419.575 11.3226 423.39C10.7558 423.852 10.0719 422.945 10.6379 422.484C18.7773 415.848 30.0892 416.7 39.0947 411.88C43.2968 409.631 46.8466 406.102 47.7097 401.259C48.4644 397.025 47.3868 392.727 46.7869 388.544C46.0544 384.24 46.3871 379.821 47.7558 375.675C49.37 371.386 51.7023 367.403 54.6529 363.896C61.1698 355.895 68.904 348.969 77.5724 343.371C87.4301 336.927 98.3175 332.217 109.764 329.444C110.472 329.273 110.602 330.402 109.898 330.572Z" fill="white"/>
</g>
<g filter="url(#filter14_d_601_3962)">
<path d="M60.8435 357.735C58.4409 355.04 57.0746 351.578 56.9887 347.968C56.9028 344.358 58.1028 340.835 60.3745 338.029C60.8361 337.462 61.7379 338.153 61.2757 338.72C59.1554 341.33 58.0379 344.611 58.1247 347.973C58.2116 351.334 59.4972 354.554 61.7494 357.05C62.2382 357.594 61.3295 358.275 60.8435 357.735Z" fill="white"/>
</g>
<g filter="url(#filter15_d_601_3962)">
<path d="M47.2793 385.8C54.6444 386.289 61.9234 383.984 67.6647 379.345C68.2331 378.885 68.9172 379.791 68.3495 380.251C62.3659 385.067 54.7862 387.45 47.123 386.925C46.3926 386.874 46.5529 385.749 47.2793 385.8Z" fill="white"/>
</g>
<g filter="url(#filter16_d_601_3962)">
<path d="M87.6853 338.411C88.3977 339.648 89.406 340.688 90.6198 341.439C91.8335 342.189 93.2147 342.627 94.6393 342.712C95.37 342.753 95.2089 343.877 94.483 343.836C92.9095 343.735 91.3847 343.25 90.0418 342.424C88.6989 341.597 87.5789 340.455 86.7795 339.096C86.6934 338.974 86.6569 338.824 86.6775 338.676C86.698 338.528 86.774 338.394 86.89 338.3C87.0104 338.21 87.1615 338.171 87.3104 338.192C87.4594 338.213 87.5941 338.291 87.6853 338.411Z" fill="white"/>
</g>
<g filter="url(#filter17_d_601_3962)">
<path d="M145.167 409.859C144.71 409.846 144.253 409.833 143.791 409.828C137.646 409.687 131.5 410.045 125.414 410.9C124.942 410.958 124.465 411.025 123.996 411.097C109.354 413.312 95.251 418.226 82.4031 425.589C77.2933 428.524 72.4142 431.843 67.808 435.518C61.447 440.594 54.9538 446.603 47.5067 449.205C46.7328 449.485 45.9423 449.717 45.1397 449.899L10.5797 426.021C10.5284 425.928 10.4713 425.842 10.4196 425.748L8.99609 424.857C9.22041 424.651 9.4575 424.442 9.68182 424.236C9.81145 424.116 9.94882 424.001 10.0784 423.881C10.1673 423.803 10.2565 423.725 10.3322 423.649C10.3617 423.622 10.3916 423.597 10.4156 423.578C10.4914 423.502 10.5697 423.44 10.64 423.372C11.962 422.2 13.2946 421.036 14.6377 419.879C14.6431 419.871 14.6432 419.871 14.6563 419.868C24.9127 411.077 35.993 403.065 47.9373 396.989C48.2967 396.806 48.6612 396.615 49.0364 396.444C54.4355 393.735 60.047 391.472 65.8148 389.677C68.9762 388.705 72.1848 387.893 75.4283 387.245C83.8123 385.586 92.405 385.257 100.891 386.269C117.808 388.288 133.891 395.696 144.376 408.846C144.644 409.183 144.905 409.514 145.167 409.859Z" fill="#F2F2F2"/>
</g>
<g filter="url(#filter18_d_601_3962)">
<path d="M144.786 410.244C125.215 401.771 103.472 399.681 82.6452 404.27C78.1442 405.178 73.8145 406.79 69.8154 409.047C65.9909 411.371 62.8584 414.676 60.7435 418.621C58.6978 422.258 57.0264 426.216 54.132 429.294C52.627 430.847 50.8106 432.064 48.8021 432.866C46.7936 433.668 44.6382 434.036 42.4774 433.947C37.0874 433.864 32.0486 431.85 27.1896 429.708C21.7946 427.331 16.2285 424.776 10.1968 425.004C9.466 425.032 9.46574 423.896 10.1955 423.869C20.6895 423.471 29.2085 430.962 39.3008 432.535C44.01 433.269 48.9691 432.589 52.5737 429.242C55.7258 426.315 57.4528 422.235 59.4924 418.534C61.4991 414.656 64.4248 411.329 68.0139 408.842C71.8852 406.39 76.1456 404.613 80.6128 403.59C90.6331 401.125 100.979 400.252 111.27 401.001C123.021 401.791 134.55 404.585 145.358 409.262C146.027 409.552 145.451 410.531 144.786 410.244Z" fill="white"/>
</g>
<g filter="url(#filter19_d_601_3962)">
<path d="M89.262 402.398C88.9665 398.799 89.9598 395.212 92.0646 392.278C94.1694 389.344 97.2486 387.254 100.752 386.381C101.462 386.206 101.766 387.301 101.055 387.475C97.7914 388.282 94.9233 390.23 92.9689 392.966C91.0145 395.702 90.1025 399.047 90.3976 402.396C90.4609 403.124 89.3249 403.121 89.262 402.398Z" fill="white"/>
</g>
<g filter="url(#filter20_d_601_3962)">
<path d="M61.5384 416.639C67.1246 421.464 74.324 424.006 81.7012 423.759C82.4321 423.734 82.4326 424.869 81.7026 424.895C74.0252 425.138 66.5384 422.477 60.7364 417.443C60.1837 416.963 60.9888 416.162 61.5384 416.639Z" fill="white"/>
</g>
<g filter="url(#filter21_d_601_3962)">
<path d="M122.332 403.129C122.156 404.545 122.334 405.983 122.852 407.313C123.369 408.643 124.208 409.824 125.295 410.75C125.853 411.223 125.047 412.024 124.493 411.554C123.297 410.526 122.372 409.22 121.797 407.752C121.222 406.284 121.016 404.697 121.196 403.131C121.201 402.981 121.262 402.84 121.367 402.734C121.472 402.629 121.614 402.567 121.763 402.562C121.913 402.563 122.057 402.623 122.164 402.729C122.27 402.835 122.331 402.979 122.332 403.129Z" fill="white"/>
</g>
<g filter="url(#filter22_d_601_3962)">
<path d="M712.664 511.593C702.975 487.719 681.148 467.487 655.441 465.762C671.542 488.692 679.013 516.579 676.532 544.488C675.536 555.248 673.309 567.111 679.357 576.066C683.12 581.638 689.561 584.946 696.14 586.331C702.72 587.716 709.523 587.393 716.239 587.063L717.765 587.762C721.745 562.306 722.354 535.466 712.664 511.593Z" fill="#F2F2F2"/>
</g>
<g filter="url(#filter23_d_601_3962)">
<path d="M655.333 466.291C673.167 477.984 686.864 495 694.478 514.921C696.193 519.18 697.227 523.683 697.539 528.264C697.7 532.737 696.66 537.17 694.528 541.105C692.613 544.813 690.225 548.385 689.245 552.495C688.775 554.606 688.756 556.792 689.19 558.911C689.624 561.03 690.501 563.033 691.764 564.789C694.796 569.246 699.248 572.348 703.708 575.23C708.659 578.43 713.853 581.676 716.977 586.841C717.356 587.466 718.305 586.842 717.927 586.217C712.491 577.232 701.551 574.232 694.689 566.666C691.487 563.136 689.33 558.619 690.145 553.768C690.857 549.526 693.317 545.841 695.288 542.103C697.424 538.295 698.596 534.022 698.7 529.657C698.622 525.075 697.764 520.539 696.163 516.245C692.715 506.519 687.758 497.396 681.476 489.21C674.357 479.828 665.686 471.733 655.838 465.274C655.228 464.875 654.727 465.895 655.333 466.291Z" fill="white"/>
</g>
<g filter="url(#filter24_d_601_3962)">
<path d="M692.405 508.362C695.573 506.631 698.024 503.829 699.318 500.458C700.612 497.087 700.666 493.366 699.47 489.959C699.225 489.271 698.144 489.618 698.388 490.308C699.508 493.478 699.458 496.944 698.246 500.081C697.035 503.218 694.742 505.818 691.782 507.412C691.139 507.76 691.765 508.707 692.405 508.362Z" fill="white"/>
</g>
<g filter="url(#filter25_d_601_3962)">
<path d="M695.747 539.352C688.646 537.338 682.565 532.721 678.717 526.422C678.337 525.797 677.388 526.421 677.768 527.045C681.785 533.592 688.122 538.384 695.517 540.464C696.221 540.662 696.448 539.549 695.747 539.352Z" fill="white"/>
</g>
<g filter="url(#filter26_d_601_3962)">
<path d="M673.618 481.14C672.531 482.065 671.232 482.706 669.836 483.005C668.441 483.304 666.993 483.252 665.623 482.853C664.921 482.647 664.694 483.76 665.392 483.965C666.908 484.399 668.507 484.454 670.049 484.127C671.592 483.8 673.031 483.101 674.241 482.089C674.363 482.004 674.447 481.874 674.478 481.728C674.508 481.582 674.482 481.43 674.404 481.303C674.321 481.178 674.192 481.09 674.044 481.06C673.897 481.029 673.744 481.058 673.618 481.14Z" fill="white"/>
</g>
<g filter="url(#filter27_d_601_3962)">
<path d="M595.469 529.112C595.903 529.254 596.338 529.395 596.776 529.546C602.61 531.478 608.278 533.882 613.723 536.732C614.148 536.946 614.575 537.169 614.992 537.394C628.038 544.401 639.669 553.77 649.295 565.023C653.121 569.504 656.601 574.27 659.704 579.28C663.989 586.198 668.084 594.04 674.224 598.993C674.859 599.517 675.525 600.001 676.22 600.443L716.795 589.57C716.875 589.499 716.957 589.437 717.038 589.366L718.677 589.006C718.535 588.736 718.382 588.46 718.24 588.19C718.159 588.033 718.068 587.879 717.986 587.722C717.929 587.619 717.871 587.515 717.825 587.418C717.806 587.384 717.787 587.349 717.77 587.324C717.725 587.227 717.672 587.142 717.628 587.054C716.777 585.506 715.913 583.962 715.037 582.42C715.035 582.411 715.035 582.411 715.023 582.404C708.318 570.677 700.576 559.407 691.368 549.67C691.091 549.377 690.812 549.075 690.516 548.787C686.342 544.421 681.817 540.404 676.988 536.775C674.338 534.797 671.588 532.954 668.751 531.253C661.413 526.873 653.43 523.675 645.098 521.776C628.486 517.992 610.849 519.563 596.554 528.425C596.189 528.651 595.832 528.876 595.469 529.112Z" fill="#F2F2F2"/>
</g>
<g filter="url(#filter28_d_601_3962)">
<path d="M595.699 529.603C616.979 528.201 638.159 533.54 656.232 544.862C660.166 547.23 663.702 550.204 666.71 553.673C669.531 557.147 671.37 561.314 672.037 565.739C672.741 569.853 672.984 574.142 674.676 578.013C675.572 579.982 676.873 581.739 678.495 583.17C680.117 584.6 682.024 585.671 684.089 586.313C689.193 588.047 694.616 587.843 699.912 587.459C705.792 587.033 711.893 586.498 717.497 588.74C718.176 589.012 718.558 587.942 717.88 587.671C708.13 583.769 697.589 587.961 687.555 586.051C682.873 585.16 678.431 582.852 676.161 578.489C674.176 574.673 673.921 570.25 673.244 566.078C672.657 561.751 671.02 557.634 668.475 554.086C665.654 550.475 662.238 547.37 658.375 544.905C649.766 539.216 640.316 534.916 630.371 532.162C619.039 528.957 607.241 527.714 595.49 528.486C594.762 528.535 594.976 529.651 595.699 529.603Z" fill="white"/>
</g>
<g filter="url(#filter29_d_601_3962)">
<path d="M650.629 540.874C652.117 537.584 652.387 533.872 651.391 530.401C650.394 526.931 648.197 523.927 645.191 521.927C644.581 521.524 643.927 522.453 644.537 522.856C647.34 524.713 649.387 527.511 650.308 530.745C651.229 533.979 650.964 537.436 649.56 540.491C649.256 541.155 650.327 541.535 650.629 540.874Z" fill="white"/>
</g>
<g filter="url(#filter30_d_601_3962)">
<path d="M671.954 563.606C665.072 566.273 657.437 566.247 650.572 563.535C649.892 563.266 649.51 564.335 650.189 564.604C657.338 567.413 665.283 567.424 672.44 564.633C673.121 564.366 672.633 563.341 671.954 563.606Z" fill="white"/>
</g>
<g filter="url(#filter31_d_601_3962)">
<path d="M524.683 592.886C529.955 592.886 534.229 588.612 534.229 583.34C534.229 578.068 529.955 573.794 524.683 573.794C519.411 573.794 515.137 578.068 515.137 583.34C515.137 588.612 519.411 592.886 524.683 592.886Z" fill="#E6E6E6"/>
</g>
<g filter="url(#filter32_d_601_3962)">
<path d="M619.237 530.449C618.926 531.842 618.275 533.136 617.341 534.215C616.406 535.294 615.219 536.124 613.885 536.631C613.2 536.889 613.689 537.914 614.37 537.658C615.841 537.091 617.152 536.173 618.186 534.983C619.221 533.793 619.949 532.368 620.306 530.832C620.351 530.69 620.341 530.536 620.278 530.401C620.214 530.267 620.101 530.161 619.963 530.106C619.821 530.056 619.665 530.064 619.529 530.129C619.393 530.193 619.288 530.308 619.237 530.449Z" fill="white"/>
</g>
<g filter="url(#filter33_d_601_3962)">
<path d="M558.495 430.296C563.767 430.296 568.041 426.022 568.041 420.75C568.041 415.478 563.767 411.204 558.495 411.204C553.223 411.204 548.949 415.478 548.949 420.75C548.949 426.022 553.223 430.296 558.495 430.296Z" fill="#E6E6E6"/>
</g>
<g filter="url(#filter34_d_601_3962)">
<path d="M380.866 165.508C386.138 165.508 390.412 161.234 390.412 155.962C390.412 150.69 386.138 146.416 380.866 146.416C375.594 146.416 371.32 150.69 371.32 155.962C371.32 161.234 375.594 165.508 380.866 165.508Z" fill="#E6E6E6"/>
</g>
<g filter="url(#filter35_d_601_3962)">
<path d="M180.75 514.859C180.75 517.289 180.83 519.719 181 522.129C181.18 525.009 181.49 527.869 181.91 530.709C183.322 540.272 186.019 549.6 189.93 558.439C190.13 558.879 190.33 559.319 190.53 559.759L190.83 560.419C190.93 560.639 191.04 560.859 191.14 561.069C191.28 561.359 191.41 561.639 191.55 561.919C191.72 562.269 191.89 562.619 192.07 562.969C192.09 563.009 192.11 563.059 192.14 563.109C192.29 563.409 192.43 563.699 192.59 563.999C192.62 564.065 192.653 564.128 192.69 564.189C192.86 564.529 193.04 564.879 193.22 565.219C193.42 565.579 193.61 565.949 193.81 566.309C194.29 567.199 194.79 568.079 195.3 568.959C195.45 569.219 195.6 569.479 195.76 569.739C195.9 569.979 196.04 570.219 196.19 570.459C196.29 570.619 196.39 570.789 196.49 570.949C196.65 571.219 196.81 571.479 196.98 571.749C197.35 572.339 197.73 572.929 198.1 573.519H198.11C198.28 573.789 198.46 574.059 198.64 574.319V574.329C199.17 575.129 199.72 575.929 200.27 576.719L200.274 576.72L200.277 576.722L200.279 576.725L200.28 576.729C200.72 577.369 201.18 577.989 201.63 578.609V578.619C201.93 579.009 202.22 579.409 202.52 579.799C202.76 580.119 203.01 580.449 203.26 580.769C203.31 580.839 203.37 580.899 203.42 580.969V580.979C203.49 581.069 203.56 581.149 203.63 581.239C204.69 582.599 205.783 583.929 206.91 585.229L207.09 585.439C207.15 585.509 207.22 585.579 207.28 585.659C207.38 585.769 207.47 585.879 207.57 585.989C207.66 586.099 207.76 586.209 207.86 586.319C213.733 592.935 220.393 598.808 227.69 603.809C228.66 604.479 229.65 605.129 230.64 605.759C230.89 605.919 231.14 606.079 231.39 606.229C231.46 606.279 231.54 606.329 231.61 606.379C231.75 606.459 231.88 606.539 232.02 606.629C232.15 606.709 232.28 606.789 232.42 606.869C232.95 607.199 233.49 607.519 234.03 607.839C234.16 607.919 234.3 607.999 234.44 608.079C234.57 608.149 234.71 608.229 234.85 608.309C234.85 608.319 234.85 608.319 234.86 608.319H234.87V608.329H234.88C235.55 608.719 236.23 609.099 236.92 609.469C237.29 609.669 237.67 609.879 238.05 610.069C238.91 610.539 239.78 610.989 240.65 611.419C240.96 611.569 241.28 611.719 241.59 611.879C241.85 611.999 242.11 612.129 242.37 612.249C242.45 612.289 242.52 612.319 242.59 612.359C242.81 612.459 243.02 612.559 243.24 612.659C243.48 612.769 243.72 612.879 243.95 612.989C244.22 613.109 244.48 613.229 244.75 613.339C245.75 613.789 246.76 614.219 247.77 614.629C251.39 616.113 255.089 617.395 258.85 618.469C259.02 618.519 259.2 618.569 259.38 618.619C260.99 619.069 262.61 619.479 264.24 619.849C267.09 620.509 269.967 621.049 272.87 621.469H272.89C273.24 621.519 273.58 621.569 273.93 621.619C273.96 621.619 273.99 621.629 274.03 621.629C274.03 621.639 274.03 621.639 274.04 621.629C274.42 621.679 274.81 621.729 275.19 621.779C275.216 621.787 275.243 621.79 275.27 621.789C275.63 621.839 276 621.879 276.36 621.919C276.75 621.959 277.14 621.999 277.53 622.039C277.83 622.069 278.13 622.099 278.43 622.129C278.456 622.138 278.483 622.141 278.51 622.139C278.78 622.159 279.05 622.189 279.32 622.209H279.36C279.59 622.229 279.83 622.249 280.06 622.269C282.85 622.489 285.65 622.589 288.49 622.589C291.56 622.589 294.61 622.469 297.62 622.209H297.65C298.3 622.149 298.94 622.089 299.58 622.029C299.75 622.009 299.91 621.999 300.08 621.979C300.11 621.979 300.15 621.969 300.18 621.969C300.23 621.959 300.28 621.959 300.34 621.949C300.367 621.95 300.394 621.947 300.42 621.939C300.64 621.919 300.86 621.889 301.09 621.869L301.84 621.779C302.09 621.749 302.34 621.709 302.59 621.679C302.65 621.669 302.7 621.669 302.76 621.659C302.94 621.639 303.12 621.609 303.3 621.579C303.39 621.569 303.48 621.559 303.56 621.549C303.74 621.519 303.91 621.499 304.09 621.469H304.1C309.588 620.679 315.004 619.459 320.3 617.819C320.58 617.729 320.86 617.649 321.14 617.559C321.15 617.559 321.16 617.549 321.17 617.549C321.25 617.519 321.34 617.499 321.42 617.469C322.17 617.229 322.91 616.979 323.65 616.719C324.67 616.379 325.68 616.009 326.69 615.629C326.96 615.519 327.23 615.419 327.5 615.309C328.05 615.099 328.59 614.879 329.13 614.659C330.05 614.289 330.97 613.899 331.88 613.499C332.73 613.119 333.57 612.739 334.41 612.339C334.43 612.329 334.46 612.329 334.48 612.309C334.67 612.219 334.86 612.129 335.04 612.039H335.05C339.655 609.829 344.097 607.293 348.34 604.449C349.7 603.539 351.04 602.599 352.36 601.629C353.42 600.839 354.46 600.039 355.5 599.219C360.631 595.138 365.38 590.599 369.69 585.659V585.649C370 585.309 370.29 584.959 370.59 584.609C370.64 584.559 370.68 584.509 370.72 584.469C370.91 584.239 371.09 584.019 371.28 583.799V583.789C371.53 583.499 371.78 583.199 372.02 582.899C372.08 582.829 372.13 582.769 372.18 582.709V582.699C372.38 582.459 372.57 582.219 372.77 581.969C372.87 581.849 372.96 581.729 373.06 581.609V581.599C373.65 580.859 374.22 580.119 374.79 579.359C374.94 579.159 375.09 578.949 375.25 578.739C375.97 577.769 376.67 576.779 377.35 575.789V575.779C377.61 575.409 377.86 575.029 378.12 574.659C378.37 574.279 378.62 573.899 378.87 573.519C379.12 573.119 379.38 572.729 379.63 572.329L379.66 572.269C379.91 571.889 380.16 571.489 380.4 571.099C380.59 570.779 380.78 570.469 380.97 570.159C381.09 569.949 381.22 569.739 381.34 569.519C381.387 569.452 381.43 569.382 381.47 569.309C381.75 568.819 382.03 568.329 382.31 567.839C382.64 567.269 382.96 566.689 383.28 566.109C383.4 565.869 383.53 565.629 383.66 565.389C383.71 565.309 383.75 565.229 383.79 565.149C383.94 564.869 384.09 564.579 384.23 564.289C384.38 564.009 384.53 563.719 384.68 563.429C384.82 563.149 384.97 562.859 385.11 562.569C385.22 562.349 385.32 562.129 385.43 561.909C385.438 561.891 385.448 561.875 385.46 561.859C385.51 561.749 385.56 561.649 385.6 561.549C385.612 561.534 385.622 561.517 385.63 561.499C385.74 561.279 385.84 561.049 385.95 560.829C386.09 560.539 386.22 560.239 386.36 559.949C386.45 559.749 386.55 559.539 386.64 559.329C386.77 559.039 386.91 558.749 387.03 558.449C387.17 558.149 387.3 557.849 387.43 557.539C387.468 557.465 387.501 557.388 387.53 557.309C387.6 557.169 387.65 557.029 387.71 556.889C387.87 556.519 388.03 556.139 388.18 555.769C388.36 555.349 388.53 554.919 388.69 554.499C388.85 554.109 389 553.709 389.15 553.309L389.21 553.169C391.54 547.032 393.307 540.696 394.49 534.239V534.229C395.157 530.543 395.634 526.825 395.92 523.089C395.93 522.879 395.95 522.679 395.96 522.479C396.14 519.949 396.22 517.409 396.22 514.859C396.22 513.729 396.2 512.609 396.17 511.489C395.472 488.066 387.119 465.516 372.39 447.289C372.07 446.889 371.75 446.489 371.42 446.089C371.09 445.689 370.75 445.299 370.42 444.909C359.458 432.071 345.632 421.989 330.058 415.475C314.484 408.961 297.598 406.197 280.76 407.406C263.922 408.616 247.603 413.765 233.12 422.438C218.637 431.111 206.395 443.066 197.38 457.339C196.89 458.119 196.4 458.909 195.93 459.699C195.45 460.499 194.99 461.299 194.53 462.109C185.471 478.213 180.724 496.383 180.75 514.859Z" fill="#F2F2F2"/>
</g>
<g filter="url(#filter36_d_601_3962)">
<path d="M284.284 451.679C302 456.225 320.155 458.845 338.433 459.495C341.197 459.645 343.967 459.626 346.729 459.438C352.798 458.928 357.996 457.02 363.786 455.978C366.569 455.478 369.67 455.113 372.472 454.573C371.824 453.769 371.165 452.976 370.494 452.192C364.452 452.703 358.316 454.374 352.201 455.15C341.807 456.472 330.161 454.997 322.281 451.36C318.458 449.594 315.529 447.391 311.72 445.618C311.202 445.352 310.64 445.18 310.062 445.111C308.964 445.078 307.879 445.359 306.935 445.922C300.544 449.148 291.264 451.771 282.651 450.328C282.009 450.83 283.229 451.403 284.284 451.679Z" fill="white"/>
</g>
<g filter="url(#filter37_d_601_3962)">
<path d="M246.17 463.034C246.091 463.02 246.012 463.009 245.934 462.995C246.264 462.79 246.576 462.555 246.864 462.294L246.17 463.034Z" fill="white"/>
</g>
<g filter="url(#filter38_d_601_3962)">
<path d="M194.609 469.4C198.207 469.687 201.823 469.901 205.458 470.042C208.222 470.191 210.992 470.172 213.754 469.986C219.823 469.473 225.022 467.568 230.811 466.525C236.117 465.572 242.565 465.112 245.934 462.995C237.227 461.488 228.205 464.556 219.227 465.698C211.964 466.591 204.602 466.227 197.463 464.623C196.468 466.187 195.517 467.779 194.609 469.4Z" fill="white"/>
</g>
<g filter="url(#filter39_d_601_3962)">
<path d="M238.051 610.069C238.911 610.539 239.781 610.989 240.651 611.419C240.961 611.569 241.281 611.719 241.591 611.879C241.851 611.999 242.111 612.129 242.371 612.249C242.451 612.289 242.521 612.319 242.591 612.359C242.811 612.459 243.021 612.559 243.241 612.659C243.481 612.769 243.721 612.879 243.951 612.989C244.221 613.109 244.481 613.229 244.751 613.339C245.751 613.789 246.761 614.219 247.771 614.629C251.39 616.113 255.089 617.395 258.851 618.469C259.021 618.519 259.201 618.569 259.381 618.619C260.991 619.069 262.611 619.479 264.241 619.849C267.091 620.509 269.968 621.049 272.871 621.469H272.891C273.241 621.519 273.581 621.569 273.931 621.619C273.961 621.619 273.991 621.629 274.031 621.629C274.031 621.639 274.031 621.639 274.041 621.629C274.421 621.679 274.811 621.729 275.191 621.779C275.217 621.787 275.244 621.79 275.271 621.789C275.631 621.839 276.001 621.879 276.361 621.919C276.751 621.959 277.141 621.999 277.531 622.039C277.831 622.069 278.131 622.099 278.431 622.129C278.457 622.138 278.484 622.142 278.511 622.139C278.781 622.159 279.051 622.189 279.321 622.209H279.361C279.591 622.229 279.831 622.249 280.061 622.269C282.851 622.489 285.651 622.589 288.491 622.589C291.561 622.589 294.611 622.469 297.621 622.209H297.651C298.301 622.149 298.941 622.089 299.581 622.029C299.751 622.009 299.911 621.999 300.081 621.979C300.111 621.979 300.151 621.969 300.181 621.969C300.231 621.959 300.281 621.959 300.341 621.949C300.368 621.95 300.395 621.947 300.421 621.939C300.641 621.919 300.861 621.889 301.091 621.869L301.841 621.779C302.091 621.749 302.341 621.709 302.591 621.679C302.651 621.669 302.701 621.669 302.761 621.659C302.941 621.639 303.121 621.609 303.301 621.579C303.391 621.569 303.481 621.559 303.561 621.549C303.741 621.519 303.911 621.499 304.091 621.469H304.101C309.588 620.679 315.005 619.459 320.301 617.819C320.581 617.729 320.861 617.649 321.141 617.559C319.85 611.538 317.191 605.893 313.372 601.063C309.552 596.232 304.673 592.344 299.111 589.699C300.486 588.389 301.437 586.696 301.841 584.839C302.1 583.642 302.121 582.406 301.901 581.202C301.682 579.997 301.226 578.847 300.561 577.819C299.263 575.827 297.227 574.431 294.901 573.939L280.081 570.839C277.695 570.374 275.222 570.87 273.2 572.218C271.177 573.566 269.769 575.658 269.281 578.039C269.13 578.769 269.063 579.514 269.081 580.259C254.811 589.759 244.541 599.609 238.051 610.069Z" fill="url(#paint0_linear_601_3962)"/>
</g>
<g filter="url(#filter40_d_601_3962)">
<path d="M281.809 564.986C298.861 564.986 312.685 551.162 312.685 534.11C312.685 517.058 298.861 503.235 281.809 503.235C264.757 503.235 250.934 517.058 250.934 534.11C250.934 551.162 264.757 564.986 281.809 564.986Z" fill="#FFB8B8"/>
</g>
<g filter="url(#filter41_d_601_3962)">
<path d="M262.566 560.73C259.273 558.481 255.911 554.159 253.666 550.92C250.692 546.662 248.645 541.827 247.659 536.727C246.673 531.628 246.769 526.378 247.941 521.319C248.315 519.296 249.234 517.413 250.599 515.874C252.053 514.435 254.428 513.752 256.203 514.77C255.719 513.473 255.506 512.09 255.576 510.707C255.647 509.325 256.001 507.971 256.615 506.73C257.862 504.253 259.67 502.1 261.894 500.442C266.148 497.167 271.21 495.104 276.541 494.472C281.873 493.841 287.276 494.665 292.178 496.857C294.106 497.751 290.939 490.849 292.957 491.514C294.963 492.291 297.192 492.252 299.169 491.405C301.068 490.449 307.361 496.128 306.633 494.131C308.073 494.785 309.325 495.793 310.271 497.061C311.218 498.33 311.828 499.817 312.044 501.384C312.261 502.952 312.078 504.548 311.511 506.026C310.945 507.503 310.013 508.813 308.804 509.834C307.211 511.179 305.221 511.954 303.476 513.096C300.42 515.246 298.256 518.442 297.394 522.079C296.532 525.716 297.031 529.542 298.798 532.836C296.962 531.657 294.948 530.781 292.833 530.244C291.772 529.985 290.664 529.983 289.603 530.239C288.541 530.494 287.555 530.999 286.728 531.712C286.037 532.503 285.515 533.427 285.194 534.428C284.873 535.428 284.759 536.483 284.861 537.528C284.93 539.624 285.429 541.688 285.485 543.783C285.649 549.933 288.942 561.623 284.174 564.892C280.412 567.471 267.061 563.801 262.566 560.73Z" fill="#2F2E41"/>
</g>
<g filter="url(#filter42_d_601_3962)">
<path d="M303.115 386.856C302.465 386.855 301.821 386.724 301.221 386.473C300.326 386.114 299.56 385.492 299.024 384.69C298.488 383.888 298.207 382.943 298.217 381.978V377.091C298.216 376.066 297.809 375.083 297.084 374.358C296.359 373.633 295.376 373.225 294.351 373.224C293.061 373.223 291.824 372.71 290.911 371.797C289.999 370.885 289.486 369.648 289.484 368.358V301.402C289.486 300.112 289.999 298.875 290.911 297.963C291.823 297.051 293.06 296.537 294.351 296.536H516.177C517.468 296.537 518.705 297.05 519.617 297.963C520.529 298.875 521.043 300.112 521.044 301.402V368.358C521.043 369.648 520.529 370.885 519.617 371.797C518.705 372.71 517.468 373.223 516.177 373.224H320.322C319.814 373.223 319.311 373.322 318.841 373.517C318.372 373.711 317.946 373.996 317.587 374.357L306.525 385.419C306.08 385.872 305.55 386.233 304.964 386.479C304.379 386.726 303.75 386.854 303.115 386.856Z" fill="white"/>
</g>
<g filter="url(#filter43_d_601_3962)">
<path d="M303.115 387.856C302.333 387.855 301.559 387.699 300.838 387.397C299.759 386.963 298.836 386.214 298.19 385.247C297.544 384.28 297.205 383.141 297.217 381.978V377.091C297.217 376.331 296.914 375.603 296.377 375.065C295.84 374.528 295.111 374.225 294.351 374.224C292.796 374.222 291.304 373.604 290.205 372.504C289.105 371.404 288.486 369.913 288.484 368.357V301.402C288.486 299.847 289.105 298.356 290.204 297.256C291.304 296.156 292.795 295.538 294.351 295.536H516.177C517.733 295.538 519.224 296.156 520.324 297.256C521.424 298.356 522.042 299.847 522.044 301.402V368.357C522.042 369.913 521.424 371.404 520.324 372.504C519.224 373.604 517.733 374.222 516.177 374.224H320.322C319.945 374.223 319.572 374.297 319.224 374.441C318.876 374.585 318.56 374.797 318.294 375.064L307.232 386.126C306.695 386.672 306.054 387.106 305.347 387.404C304.64 387.701 303.881 387.854 303.115 387.856ZM294.351 297.536C293.326 297.537 292.343 297.945 291.618 298.67C290.893 299.395 290.485 300.377 290.484 301.402V368.357C290.485 369.382 290.893 370.365 291.618 371.09C292.343 371.815 293.326 372.223 294.351 372.224C295.641 372.226 296.878 372.739 297.791 373.652C298.703 374.564 299.216 375.801 299.217 377.091V381.978C299.218 382.743 299.444 383.49 299.869 384.126C300.294 384.762 300.898 385.257 301.605 385.55C302.311 385.842 303.088 385.919 303.838 385.77C304.588 385.62 305.277 385.252 305.818 384.712L316.88 373.65C317.331 373.197 317.867 372.837 318.458 372.592C319.049 372.348 319.682 372.222 320.322 372.224H516.177C517.203 372.223 518.185 371.815 518.91 371.09C519.635 370.365 520.043 369.382 520.044 368.357V301.402C520.043 300.377 519.635 299.394 518.91 298.669C518.185 297.945 517.202 297.537 516.177 297.536L294.351 297.536Z" fill="#3F3D56"/>
</g>
<g filter="url(#filter44_d_601_3962)">
<path d="M497.114 326.848H317.235C316.548 326.848 315.889 326.575 315.403 326.089C314.917 325.604 314.645 324.945 314.645 324.257C314.645 323.57 314.917 322.911 315.403 322.425C315.889 321.94 316.548 321.667 317.235 321.667H497.114C497.801 321.667 498.46 321.94 498.946 322.425C499.432 322.911 499.705 323.57 499.705 324.257C499.705 324.945 499.432 325.604 498.946 326.089C498.46 326.575 497.801 326.848 497.114 326.848Z" fill="#E41165"/>
</g>
<g filter="url(#filter45_d_601_3962)">
<path d="M496.783 337.855H317.235C316.548 337.855 315.889 337.582 315.403 337.096C314.917 336.61 314.645 335.951 314.645 335.264C314.645 334.577 314.917 333.918 315.403 333.432C315.889 332.946 316.548 332.673 317.235 332.673H496.783C497.47 332.673 498.129 332.946 498.615 333.432C499.101 333.918 499.374 334.577 499.374 335.264C499.374 335.951 499.101 336.61 498.615 337.096C498.129 337.582 497.47 337.855 496.783 337.855Z" fill="#E6E6E6"/>
</g>
<g filter="url(#filter46_d_601_3962)">
<path d="M496.604 348.861H317.235C316.548 348.861 315.889 348.588 315.403 348.103C314.917 347.617 314.645 346.958 314.645 346.271C314.645 345.584 314.917 344.925 315.403 344.439C315.889 343.953 316.548 343.68 317.235 343.68H496.604C497.292 343.68 497.951 343.953 498.436 344.439C498.922 344.925 499.195 345.584 499.195 346.271C499.195 346.958 498.922 347.617 498.436 348.103C497.951 348.588 497.292 348.861 496.604 348.861Z" fill="#E6E6E6"/>
</g>
<g filter="url(#filter47_d_601_3962)">
<path d="M780.384 286.146C780.384 288.576 780.304 291.006 780.134 293.416C779.954 296.296 779.644 299.156 779.224 301.996C777.813 311.558 775.115 320.886 771.204 329.726C771.004 330.166 770.804 330.606 770.604 331.046L770.304 331.706C770.204 331.926 770.094 332.146 769.994 332.356C769.854 332.646 769.724 332.926 769.584 333.206C769.414 333.556 769.244 333.906 769.064 334.256C769.044 334.296 769.024 334.346 768.994 334.396C768.844 334.696 768.704 334.986 768.544 335.286C768.515 335.351 768.481 335.414 768.444 335.476C768.274 335.816 768.094 336.166 767.914 336.506C767.714 336.866 767.524 337.236 767.324 337.596C766.844 338.486 766.344 339.366 765.834 340.246C765.684 340.506 765.534 340.766 765.374 341.026C765.234 341.266 765.094 341.506 764.944 341.746C764.844 341.906 764.744 342.076 764.644 342.236C764.484 342.506 764.324 342.766 764.154 343.036C763.784 343.626 763.404 344.216 763.034 344.806H763.024C762.854 345.076 762.674 345.346 762.494 345.606V345.616C761.964 346.416 761.414 347.216 760.864 348.006C760.863 348.006 760.861 348.006 760.86 348.006C760.859 348.007 760.858 348.008 760.857 348.008C760.856 348.009 760.855 348.01 760.855 348.012C760.854 348.013 760.854 348.014 760.854 348.016C760.414 348.656 759.954 349.276 759.504 349.896V349.906C759.204 350.296 758.914 350.696 758.614 351.086C758.374 351.406 758.124 351.736 757.874 352.056C757.824 352.126 757.764 352.186 757.714 352.256V352.266C757.644 352.356 757.574 352.436 757.504 352.526C756.444 353.886 755.351 355.216 754.224 356.516L754.044 356.726C753.984 356.796 753.914 356.866 753.854 356.946C753.754 357.056 753.664 357.166 753.564 357.276C753.474 357.386 753.374 357.496 753.274 357.606C747.401 364.221 740.742 370.095 733.444 375.096C732.474 375.766 731.484 376.416 730.494 377.046C730.244 377.206 729.994 377.366 729.744 377.516C729.674 377.566 729.594 377.616 729.524 377.666C729.384 377.746 729.254 377.826 729.114 377.916C728.984 377.996 728.854 378.076 728.714 378.156C728.184 378.486 727.644 378.806 727.104 379.126C726.974 379.206 726.834 379.286 726.694 379.366C726.564 379.436 726.424 379.516 726.284 379.596C726.284 379.606 726.284 379.606 726.274 379.606H726.264V379.616H726.254C725.584 380.006 724.904 380.386 724.214 380.756C723.844 380.956 723.464 381.166 723.084 381.356C722.224 381.826 721.354 382.276 720.484 382.706C720.174 382.856 719.854 383.006 719.544 383.166C719.284 383.286 719.024 383.416 718.764 383.536C718.684 383.576 718.614 383.606 718.544 383.646C718.324 383.746 718.114 383.846 717.894 383.946C717.654 384.056 717.414 384.166 717.184 384.276C716.914 384.396 716.654 384.516 716.384 384.626C715.384 385.076 714.374 385.506 713.364 385.916C709.745 387.399 706.046 388.681 702.284 389.756C702.114 389.806 701.934 389.856 701.754 389.906C700.144 390.356 698.524 390.766 696.894 391.136C694.044 391.796 691.167 392.336 688.264 392.756H688.244C687.894 392.806 687.554 392.856 687.204 392.906C687.174 392.906 687.144 392.916 687.104 392.916C687.104 392.926 687.104 392.926 687.094 392.916C686.714 392.966 686.324 393.016 685.944 393.066C685.918 393.073 685.891 393.077 685.864 393.076C685.504 393.126 685.134 393.166 684.774 393.206C684.384 393.246 683.994 393.286 683.604 393.326C683.304 393.356 683.004 393.386 682.704 393.416C682.678 393.424 682.651 393.428 682.624 393.426C682.354 393.446 682.084 393.476 681.814 393.496H681.774C681.544 393.516 681.304 393.536 681.074 393.556C678.284 393.776 675.484 393.876 672.644 393.876C669.574 393.876 666.524 393.756 663.514 393.496H663.484C662.834 393.436 662.194 393.376 661.554 393.316C661.384 393.296 661.224 393.286 661.054 393.266C661.024 393.266 660.984 393.256 660.954 393.256C660.904 393.246 660.854 393.246 660.794 393.236C660.767 393.237 660.74 393.233 660.714 393.226C660.494 393.206 660.274 393.176 660.044 393.156L659.294 393.066C659.044 393.036 658.794 392.996 658.544 392.966C658.484 392.956 658.434 392.956 658.374 392.946C658.194 392.926 658.014 392.896 657.834 392.866C657.744 392.856 657.654 392.846 657.574 392.836C657.394 392.806 657.224 392.786 657.044 392.756H657.034C651.547 391.966 646.13 390.745 640.834 389.106C640.554 389.016 640.274 388.936 639.994 388.846C639.984 388.846 639.974 388.836 639.964 388.836C639.884 388.806 639.794 388.786 639.714 388.756C638.964 388.516 638.224 388.266 637.484 388.006C636.464 387.666 635.454 387.296 634.444 386.916C634.174 386.806 633.904 386.706 633.634 386.596C633.084 386.386 632.544 386.166 632.004 385.946C631.084 385.576 630.164 385.186 629.254 384.786C628.404 384.406 627.564 384.026 626.724 383.626C626.704 383.616 626.674 383.616 626.654 383.596C626.464 383.506 626.274 383.416 626.094 383.326H626.084C621.479 381.116 617.038 378.579 612.794 375.736C611.434 374.826 610.094 373.886 608.774 372.916C607.714 372.126 606.674 371.326 605.634 370.506C600.503 366.425 595.754 361.886 591.444 356.946V356.936C591.134 356.596 590.844 356.246 590.544 355.896C590.494 355.846 590.454 355.796 590.414 355.756C590.224 355.526 590.044 355.306 589.854 355.086V355.076C589.604 354.786 589.354 354.486 589.114 354.186C589.054 354.116 589.004 354.056 588.954 353.996V353.986C588.754 353.746 588.564 353.506 588.364 353.256C588.264 353.136 588.174 353.016 588.074 352.896V352.886C587.484 352.146 586.914 351.406 586.344 350.646C586.194 350.446 586.044 350.236 585.884 350.026C585.164 349.056 584.464 348.066 583.784 347.076V347.066C583.524 346.696 583.274 346.316 583.014 345.946C582.764 345.566 582.514 345.186 582.264 344.806C582.014 344.406 581.754 344.016 581.504 343.616L581.474 343.556C581.224 343.176 580.974 342.776 580.734 342.386C580.544 342.066 580.354 341.756 580.164 341.446C580.044 341.236 579.914 341.026 579.794 340.806C579.747 340.738 579.704 340.668 579.664 340.596C579.384 340.106 579.104 339.616 578.824 339.126C578.494 338.556 578.174 337.976 577.854 337.396C577.734 337.156 577.604 336.916 577.474 336.676C577.424 336.596 577.384 336.516 577.344 336.436C577.194 336.156 577.044 335.866 576.904 335.576C576.754 335.296 576.604 335.006 576.454 334.716C576.314 334.436 576.164 334.146 576.024 333.856C575.914 333.636 575.814 333.416 575.704 333.196C575.696 333.178 575.686 333.161 575.674 333.146C575.624 333.036 575.574 332.936 575.534 332.836C575.522 332.82 575.512 332.804 575.504 332.786C575.394 332.566 575.294 332.336 575.184 332.116C575.044 331.826 574.914 331.526 574.774 331.236C574.684 331.036 574.584 330.826 574.494 330.616C574.364 330.326 574.224 330.036 574.104 329.736C573.964 329.436 573.834 329.136 573.704 328.826C573.667 328.751 573.633 328.674 573.604 328.596C573.534 328.456 573.484 328.316 573.424 328.176C573.264 327.806 573.104 327.426 572.954 327.056C572.774 326.636 572.604 326.206 572.444 325.786C572.284 325.396 572.134 324.996 571.984 324.596L571.924 324.456C569.594 318.319 567.827 311.983 566.644 305.526V305.516C565.977 301.829 565.5 298.111 565.214 294.376C565.204 294.166 565.184 293.966 565.174 293.766C564.994 291.236 564.914 288.696 564.914 286.146C564.914 285.016 564.934 283.896 564.964 282.776C565.662 259.352 574.015 236.802 588.744 218.576C589.064 218.176 589.384 217.776 589.714 217.376C590.044 216.976 590.384 216.586 590.714 216.196C601.676 203.358 615.502 193.275 631.076 186.761C646.65 180.247 663.536 177.483 680.374 178.693C697.213 179.902 713.531 185.051 728.014 193.724C742.497 202.398 754.739 214.353 763.754 228.626C764.244 229.406 764.734 230.196 765.204 230.986C765.684 231.786 766.144 232.586 766.604 233.396C775.663 249.499 780.41 267.669 780.384 286.146Z" fill="#F2F2F2"/>
</g>
<g filter="url(#filter48_d_601_3962)">
<path d="M676.93 215.679C659.214 220.225 641.059 222.845 622.781 223.495C620.017 223.645 617.247 223.626 614.485 223.438C608.416 222.928 603.218 221.02 597.428 219.978C594.645 219.478 591.544 219.113 588.742 218.573C589.39 217.769 590.049 216.976 590.719 216.192C596.762 216.703 602.898 218.374 609.013 219.15C619.407 220.472 631.053 218.997 638.933 215.36C642.756 213.594 645.684 211.391 649.494 209.618C650.012 209.352 650.573 209.18 651.152 209.111C652.25 209.078 653.335 209.359 654.279 209.922C660.67 213.148 669.95 215.771 678.563 214.328C679.205 214.83 677.985 215.403 676.93 215.679Z" fill="white"/>
</g>
<g filter="url(#filter49_d_601_3962)">
<path d="M715.042 227.034C715.121 227.02 715.2 227.009 715.279 226.995C714.948 226.79 714.636 226.555 714.348 226.294L715.042 227.034Z" fill="white"/>
</g>
<g filter="url(#filter50_d_601_3962)">
<path d="M766.602 233.4C763.005 233.688 759.389 233.902 755.754 234.042C752.99 234.191 750.219 234.173 747.457 233.986C741.388 233.473 736.19 231.568 730.4 230.525C725.094 229.572 718.646 229.112 715.277 226.995C723.985 225.488 733.006 228.556 741.985 229.698C749.247 230.591 756.61 230.227 763.749 228.623C764.743 230.187 765.694 231.779 766.602 233.4Z" fill="white"/>
</g>
<g filter="url(#filter51_d_601_3962)">
<path d="M660.499 333.287C678.37 333.287 692.858 318.477 692.858 300.207C692.858 281.938 678.37 267.128 660.499 267.128C642.628 267.128 628.141 281.938 628.141 300.207C628.141 318.477 642.628 333.287 660.499 333.287Z" fill="#FFB6B6"/>
</g>
<g filter="url(#filter52_d_601_3962)">
<path d="M723.086 381.356C722.226 381.826 721.356 382.276 720.486 382.706C720.176 382.856 719.856 383.006 719.546 383.166C719.286 383.286 719.026 383.416 718.766 383.536C718.686 383.576 718.616 383.606 718.546 383.646C718.326 383.746 718.116 383.846 717.896 383.946C717.656 384.056 717.416 384.166 717.186 384.276C716.916 384.396 716.656 384.516 716.386 384.626C715.386 385.076 714.376 385.506 713.366 385.916C709.747 387.399 706.048 388.681 702.286 389.756C702.116 389.806 701.936 389.856 701.756 389.906C700.146 390.356 698.526 390.766 696.896 391.136C694.046 391.796 691.169 392.336 688.266 392.756H688.246C687.896 392.806 687.556 392.856 687.206 392.906C687.176 392.906 687.146 392.916 687.106 392.916C687.106 392.926 687.106 392.926 687.096 392.916C686.716 392.966 686.326 393.016 685.946 393.066C685.92 393.073 685.893 393.077 685.866 393.076C685.506 393.126 685.136 393.166 684.776 393.206C684.386 393.246 683.996 393.286 683.606 393.326C683.306 393.356 683.006 393.386 682.706 393.416C682.68 393.424 682.653 393.428 682.626 393.426C682.356 393.446 682.086 393.476 681.816 393.496H681.776C681.546 393.516 681.306 393.536 681.076 393.556C678.286 393.776 675.486 393.876 672.646 393.876C669.576 393.876 666.526 393.756 663.516 393.496H663.486C662.836 393.436 662.196 393.376 661.556 393.316C661.386 393.296 661.226 393.286 661.056 393.266C661.026 393.266 660.986 393.256 660.956 393.256C660.906 393.246 660.856 393.246 660.796 393.236C660.769 393.237 660.742 393.233 660.716 393.226C660.496 393.206 660.276 393.176 660.046 393.156L659.296 393.066C659.046 393.036 658.796 392.996 658.546 392.966C658.486 392.956 658.436 392.956 658.376 392.946C658.196 392.926 658.016 392.896 657.836 392.866C657.746 392.856 657.656 392.846 657.576 392.836C657.396 392.806 657.226 392.786 657.046 392.756H657.036C651.549 391.966 646.132 390.745 640.836 389.106C640.556 389.016 640.276 388.936 639.996 388.846C641.287 382.824 643.945 377.18 647.765 372.349C651.585 367.518 656.464 363.63 662.026 360.986C660.65 359.675 659.699 357.982 659.296 356.126C659.036 354.929 659.016 353.693 659.236 352.488C659.455 351.283 659.911 350.134 660.576 349.106C661.874 347.113 663.91 345.717 666.236 345.226L681.056 342.126C683.442 341.661 685.915 342.156 687.937 343.504C689.959 344.853 691.368 346.945 691.856 349.326C692.007 350.056 692.074 350.8 692.056 351.546C706.326 361.046 716.596 370.896 723.086 381.356Z" fill="url(#paint1_linear_601_3962)"/>
</g>
<g filter="url(#filter53_d_601_3962)">
<path d="M747.09 285.861C741.589 279.447 738.974 270.937 734.577 263.653C730.18 256.369 722.529 249.85 714.445 251.545C708.873 252.714 704.566 257.631 702.386 263.113C700.206 268.595 699.797 274.641 699.415 280.563C699.55 268.392 691.532 256.593 680.486 252.709C669.441 248.826 656.221 253.158 649.281 262.936C646.252 260.128 642.298 258.53 638.169 258.443C634.04 258.357 630.023 259.788 626.879 262.466C620.671 268.014 619.297 278.575 623.863 285.653C628.428 292.73 638.316 295.367 645.572 291.442L648.137 293.801C646.694 298.852 649.494 304.817 654.201 306.718C658.018 308.26 663.406 308.003 664.834 312.009C665.832 314.807 663.967 317.795 661.982 319.945C659.998 322.096 657.642 324.317 657.404 327.293C657.044 331.784 661.684 334.929 665.902 335.876C676.074 338.161 687.36 333.451 693.222 324.474C693.335 324.301 693.458 324.128 693.591 323.954C696.88 319.748 699.277 314.916 700.635 309.752C701.994 304.588 702.285 299.201 701.491 293.921C701.162 291.846 701.494 290.303 702.924 289.812C709.107 287.688 726.429 292.145 731.671 296.174C736.913 300.202 741.111 305.533 746.161 309.822C751.211 314.11 757.645 317.418 764.057 316.25C769.192 315.315 773.56 311.587 776.499 307.09C779.437 302.593 781.128 297.352 782.643 292.143C771.288 298.702 755.747 295.956 747.09 285.861Z" fill="#2F2E41"/>
</g>
<g filter="url(#filter54_d_601_3962)">
<path d="M82.8243 219.146C82.8243 221.576 82.9043 224.006 83.0743 226.416C83.2543 229.296 83.5643 232.156 83.9843 234.996C85.3958 244.558 88.0936 253.886 92.0043 262.726C92.2043 263.166 92.4043 263.606 92.6043 264.046L92.9043 264.706C93.0043 264.926 93.1143 265.146 93.2143 265.356C93.3543 265.646 93.4843 265.926 93.6243 266.206C93.7943 266.556 93.9643 266.906 94.1443 267.256C94.1643 267.296 94.1843 267.346 94.2143 267.396C94.3643 267.696 94.5043 267.986 94.6643 268.286C94.6937 268.351 94.7271 268.414 94.7643 268.476C94.9343 268.816 95.1143 269.166 95.2943 269.506C95.4943 269.866 95.6843 270.236 95.8843 270.596C96.3643 271.486 96.8643 272.366 97.3743 273.246C97.5243 273.506 97.6743 273.766 97.8343 274.026C97.9743 274.266 98.1143 274.506 98.2643 274.746C98.3643 274.906 98.4643 275.076 98.5643 275.236C98.7243 275.506 98.8843 275.766 99.0543 276.036C99.4243 276.626 99.8043 277.216 100.174 277.806H100.184C100.354 278.076 100.534 278.346 100.714 278.606V278.616C101.244 279.416 101.794 280.216 102.344 281.006C102.346 281.006 102.347 281.006 102.348 281.006C102.349 281.007 102.351 281.008 102.351 281.008C102.352 281.009 102.353 281.01 102.354 281.012C102.354 281.013 102.354 281.014 102.354 281.016C102.794 281.656 103.254 282.276 103.704 282.896V282.906C104.004 283.296 104.294 283.696 104.594 284.086C104.834 284.406 105.084 284.736 105.334 285.056C105.384 285.125 105.444 285.186 105.494 285.256V285.266C105.564 285.356 105.634 285.436 105.704 285.526C106.764 286.886 107.858 288.216 108.984 289.516L109.164 289.726C109.224 289.796 109.294 289.866 109.354 289.946C109.454 290.056 109.544 290.166 109.644 290.276C109.734 290.386 109.834 290.496 109.934 290.606C115.807 297.221 122.467 303.095 129.764 308.096C130.734 308.766 131.724 309.416 132.714 310.046C132.964 310.206 133.214 310.366 133.464 310.516C133.534 310.566 133.614 310.616 133.684 310.666C133.824 310.746 133.954 310.826 134.094 310.916C134.224 310.996 134.354 311.076 134.494 311.156C135.024 311.486 135.564 311.806 136.104 312.126C136.234 312.206 136.374 312.286 136.514 312.366C136.644 312.436 136.784 312.516 136.924 312.596C136.924 312.606 136.924 312.606 136.934 312.606H136.944V312.616H136.954C137.624 313.006 138.304 313.386 138.994 313.756C139.364 313.956 139.744 314.166 140.124 314.356C140.984 314.826 141.854 315.276 142.724 315.706C143.034 315.856 143.354 316.006 143.664 316.166C143.924 316.286 144.184 316.416 144.444 316.536C144.524 316.576 144.594 316.606 144.664 316.646C144.884 316.746 145.094 316.846 145.314 316.946C145.554 317.056 145.794 317.166 146.024 317.276C146.294 317.396 146.554 317.516 146.824 317.626C147.824 318.076 148.834 318.506 149.844 318.916C153.464 320.399 157.163 321.681 160.924 322.756C161.094 322.806 161.274 322.856 161.454 322.906C163.064 323.356 164.684 323.766 166.314 324.136C169.164 324.796 172.041 325.336 174.944 325.756H174.964C175.314 325.806 175.654 325.856 176.004 325.906C176.034 325.906 176.064 325.916 176.104 325.916C176.104 325.926 176.104 325.926 176.114 325.916C176.494 325.966 176.884 326.016 177.264 326.066C177.29 326.073 177.317 326.077 177.344 326.076C177.704 326.126 178.074 326.166 178.434 326.206C178.824 326.246 179.214 326.286 179.604 326.326C179.904 326.356 180.204 326.386 180.504 326.416C180.53 326.424 180.557 326.428 180.584 326.426C180.854 326.446 181.124 326.476 181.394 326.496H181.434C181.664 326.516 181.904 326.536 182.134 326.556C184.924 326.776 187.724 326.876 190.564 326.876C193.634 326.876 196.684 326.756 199.694 326.496H199.724C200.374 326.436 201.014 326.376 201.654 326.316C201.824 326.296 201.984 326.286 202.154 326.266C202.184 326.266 202.224 326.256 202.254 326.256C202.304 326.246 202.354 326.246 202.414 326.236C202.441 326.237 202.468 326.233 202.494 326.226C202.714 326.206 202.934 326.176 203.164 326.156L203.914 326.066C204.164 326.036 204.414 325.996 204.664 325.966C204.724 325.956 204.774 325.956 204.834 325.946C205.014 325.926 205.194 325.896 205.374 325.866C205.464 325.856 205.554 325.846 205.634 325.836C205.814 325.806 205.984 325.786 206.164 325.756H206.174C211.662 324.966 217.078 323.745 222.374 322.106C222.654 322.016 222.934 321.936 223.214 321.846C223.224 321.846 223.234 321.836 223.244 321.836C223.324 321.806 223.414 321.786 223.494 321.756C224.244 321.516 224.984 321.266 225.724 321.006C226.744 320.666 227.754 320.296 228.764 319.916C229.034 319.806 229.304 319.706 229.574 319.596C230.124 319.386 230.664 319.166 231.204 318.946C232.124 318.576 233.044 318.186 233.954 317.786C234.804 317.406 235.644 317.026 236.484 316.626C236.504 316.616 236.534 316.616 236.554 316.596C236.744 316.506 236.934 316.416 237.114 316.326H237.124C241.73 314.116 246.171 311.579 250.414 308.736C251.774 307.826 253.114 306.886 254.434 305.916C255.494 305.126 256.534 304.326 257.574 303.506C262.705 299.425 267.455 294.886 271.764 289.946V289.936C272.074 289.596 272.364 289.246 272.664 288.896C272.714 288.846 272.754 288.796 272.794 288.756C272.984 288.526 273.164 288.306 273.354 288.086V288.076C273.604 287.786 273.854 287.486 274.094 287.186C274.154 287.116 274.204 287.056 274.254 286.996V286.986C274.454 286.746 274.644 286.506 274.844 286.256C274.944 286.136 275.034 286.016 275.134 285.896V285.886C275.724 285.146 276.294 284.406 276.864 283.646C277.014 283.446 277.164 283.236 277.324 283.026C278.044 282.056 278.744 281.066 279.424 280.076V280.066C279.684 279.696 279.934 279.316 280.194 278.946C280.444 278.566 280.694 278.186 280.944 277.806C281.194 277.406 281.454 277.016 281.704 276.616L281.734 276.556C281.984 276.176 282.234 275.776 282.474 275.386C282.664 275.066 282.854 274.756 283.044 274.446C283.164 274.236 283.294 274.026 283.414 273.806C283.461 273.738 283.505 273.668 283.544 273.596C283.824 273.106 284.104 272.616 284.384 272.126C284.714 271.556 285.034 270.976 285.354 270.396C285.474 270.156 285.604 269.916 285.734 269.676C285.784 269.596 285.824 269.516 285.864 269.436C286.014 269.156 286.164 268.866 286.304 268.576C286.454 268.296 286.604 268.006 286.754 267.716C286.894 267.436 287.044 267.146 287.184 266.856C287.294 266.636 287.394 266.416 287.504 266.196C287.512 266.178 287.522 266.161 287.534 266.146C287.584 266.036 287.634 265.936 287.674 265.836C287.687 265.82 287.697 265.804 287.704 265.786C287.814 265.566 287.914 265.336 288.024 265.116C288.164 264.826 288.294 264.526 288.434 264.236C288.524 264.036 288.624 263.826 288.714 263.616C288.844 263.326 288.984 263.036 289.104 262.736C289.244 262.436 289.374 262.136 289.504 261.826C289.542 261.751 289.575 261.674 289.604 261.596C289.674 261.456 289.724 261.316 289.784 261.176C289.944 260.806 290.104 260.426 290.254 260.056C290.434 259.636 290.604 259.206 290.764 258.786C290.924 258.396 291.074 257.996 291.224 257.596L291.284 257.456C293.614 251.319 295.382 244.983 296.564 238.526V238.516C297.231 234.829 297.709 231.111 297.994 227.376C298.004 227.166 298.024 226.966 298.034 226.766C298.214 224.236 298.294 221.696 298.294 219.146C298.294 218.016 298.274 216.896 298.244 215.776C297.546 192.352 289.194 169.802 274.464 151.576C274.144 151.176 273.824 150.776 273.494 150.376C273.164 149.976 272.824 149.586 272.494 149.196C261.532 136.358 247.707 126.275 232.133 119.761C216.559 113.247 199.672 110.483 182.834 111.693C165.996 112.902 149.678 118.051 135.195 126.724C120.712 135.398 108.469 147.353 99.4544 161.626C98.9644 162.406 98.4744 163.196 98.0044 163.986C97.5244 164.786 97.0644 165.586 96.6044 166.396C87.545 182.499 82.7984 200.669 82.8243 219.146Z" fill="#F2F2F2"/>
</g>
<g filter="url(#filter55_d_601_3962)">
<path d="M186.284 148.679C204 153.225 222.155 155.845 240.433 156.495C243.197 156.645 245.967 156.626 248.729 156.438C254.798 155.928 259.996 154.02 265.786 152.978C268.569 152.478 271.67 152.113 274.472 151.573C273.824 150.769 273.165 149.976 272.494 149.192C266.452 149.703 260.316 151.374 254.201 152.15C243.807 153.472 232.161 151.997 224.281 148.36C220.458 146.594 217.529 144.391 213.72 142.618C213.202 142.352 212.64 142.18 212.062 142.111C210.964 142.078 209.879 142.359 208.935 142.922C202.544 146.148 193.264 148.771 184.651 147.328C184.009 147.83 185.229 148.403 186.284 148.679Z" fill="white"/>
</g>
<g filter="url(#filter56_d_601_3962)">
<path d="M148.17 160.034C148.091 160.02 148.012 160.009 147.934 159.995C148.264 159.79 148.576 159.555 148.864 159.294L148.17 160.034Z" fill="white"/>
</g>
<g filter="url(#filter57_d_601_3962)">
<path d="M96.6094 166.4C100.207 166.688 103.823 166.902 107.458 167.042C110.222 167.191 112.992 167.173 115.754 166.986C121.823 166.473 127.022 164.568 132.811 163.525C138.117 162.572 144.565 162.112 147.934 159.995C139.227 158.488 130.205 161.556 121.227 162.698C113.964 163.591 106.602 163.227 99.4625 161.623C98.4685 163.187 97.5174 164.779 96.6094 166.4Z" fill="white"/>
</g>
<g filter="url(#filter58_d_601_3962)">
<path d="M140.125 314.356C140.985 314.826 141.855 315.276 142.725 315.706C143.035 315.856 143.355 316.006 143.665 316.166C143.925 316.286 144.185 316.416 144.445 316.536C144.525 316.576 144.595 316.606 144.665 316.646C144.885 316.746 145.095 316.846 145.315 316.946C145.555 317.056 145.795 317.166 146.025 317.276C146.295 317.396 146.555 317.516 146.825 317.626C147.825 318.076 148.835 318.506 149.845 318.916C153.465 320.399 157.164 321.681 160.925 322.756C161.095 322.806 161.275 322.856 161.455 322.906C163.065 323.356 164.685 323.766 166.315 324.136C169.165 324.796 172.042 325.336 174.945 325.756H174.965C175.315 325.806 175.655 325.856 176.005 325.906C176.035 325.906 176.065 325.916 176.105 325.916C176.105 325.926 176.105 325.926 176.115 325.916C176.495 325.966 176.885 326.016 177.265 326.066C177.291 326.073 177.318 326.077 177.345 326.076C177.705 326.126 178.075 326.166 178.435 326.206C178.825 326.246 179.215 326.286 179.605 326.326C179.905 326.356 180.205 326.386 180.505 326.416C180.531 326.425 180.558 326.428 180.585 326.426C180.855 326.446 181.125 326.476 181.395 326.496H181.435C181.665 326.516 181.905 326.536 182.135 326.556C184.925 326.776 187.725 326.876 190.565 326.876C193.635 326.876 196.685 326.756 199.695 326.496H199.725C200.375 326.436 201.015 326.376 201.655 326.316C201.825 326.296 201.985 326.286 202.155 326.266C202.185 326.266 202.225 326.256 202.255 326.256C202.305 326.246 202.355 326.246 202.415 326.236C202.442 326.237 202.469 326.233 202.495 326.226C202.715 326.206 202.935 326.176 203.165 326.156L203.915 326.066C204.165 326.036 204.415 325.996 204.665 325.966C204.725 325.956 204.775 325.956 204.835 325.946C205.015 325.926 205.195 325.896 205.375 325.866C205.465 325.856 205.555 325.846 205.635 325.836C205.815 325.806 205.985 325.786 206.165 325.756H206.175C211.662 324.966 217.079 323.745 222.375 322.106C222.655 322.016 222.935 321.936 223.215 321.846C221.924 315.824 219.266 310.18 215.446 305.349C211.626 300.518 206.747 296.63 201.185 293.986C202.561 292.675 203.512 290.982 203.915 289.126C204.175 287.929 204.195 286.693 203.976 285.488C203.756 284.283 203.3 283.134 202.635 282.106C201.337 280.113 199.302 278.717 196.975 278.226L182.155 275.126C179.769 274.661 177.297 275.156 175.274 276.504C173.252 277.853 171.843 279.945 171.355 282.326C171.204 283.056 171.137 283.8 171.155 284.546C156.885 294.046 146.615 303.896 140.125 314.356Z" fill="url(#paint2_linear_601_3962)"/>
</g>
<g filter="url(#filter59_d_601_3962)">
<path d="M198.753 265.122C218.676 265.122 234.826 248.971 234.826 229.049C234.826 209.126 218.676 192.976 198.753 192.976C178.83 192.976 162.68 209.126 162.68 229.049C162.68 248.971 178.83 265.122 198.753 265.122Z" fill="#9E616A"/>
</g>
<g filter="url(#filter60_d_601_3962)">
<path d="M191.629 263.194C182.598 265.633 172.642 262.39 165.594 256.239C158.547 250.088 154.138 241.372 151.598 232.369C149.737 225.776 148.855 218.456 151.791 212.266C154.727 206.076 162.628 201.9 168.724 205.027C164.964 200.645 166.842 193.399 171.234 189.651C175.626 185.903 181.657 184.828 187.401 184.241C195.737 183.388 204.347 183.293 212.28 185.994C220.212 188.694 227.419 194.598 229.902 202.602C232.437 203.782 235.293 204.089 238.021 203.473C240.749 202.857 243.196 201.353 244.978 199.198C245.438 201.673 245.069 204.231 243.928 206.476C242.788 208.72 240.939 210.527 238.669 211.615L250.822 207.675C252.913 212.099 249.25 217.575 244.567 218.991C239.883 220.408 234.827 218.86 230.383 216.813C225.938 214.766 221.631 212.178 216.803 211.38C211.976 210.581 206.305 212.133 204.072 216.486C202.903 218.767 202.842 221.438 202.162 223.909C201.483 226.38 199.763 228.942 197.208 229.145C195.594 229.272 194.014 228.413 192.404 228.588C191.416 228.783 190.507 229.261 189.787 229.965C189.066 230.669 188.567 231.567 188.35 232.55C187.949 234.523 187.944 236.555 188.335 238.53L191.423 262.128L191.629 263.194Z" fill="#2F2E41"/>
</g>
</g>
<defs>
<filter id="filter0_d_601_3962" x="118.211" y="230.073" width="143.957" height="192.237" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter1_d_601_3962" x="210.844" y="449.913" width="330.148" height="100.263" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter2_d_601_3962" x="444.672" y="247.338" width="262.934" height="287.831" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter3_d_601_3962" x="52.082" y="396.461" width="203.676" height="154.713" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter4_d_601_3962" x="534.055" y="584.393" width="191.273" height="35.7303" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter5_d_601_3962" x="215.07" y="189.841" width="303.898" height="50.3725" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter6_d_601_3962" x="62.1289" y="233.653" width="143.957" height="192.237" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter7_d_601_3962" x="154.762" y="453.493" width="330.148" height="100.263" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter8_d_601_3962" x="388.586" y="250.918" width="262.934" height="287.831" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter9_d_601_3962" x="-4" y="400.041" width="203.676" height="154.713" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter10_d_601_3962" x="477.973" y="587.972" width="191.273" height="35.7303" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter11_d_601_3962" x="158.988" y="193.42" width="303.898" height="50.3725" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter12_d_601_3962" x="6.27344" y="327.986" width="107.703" height="110.244" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter13_d_601_3962" x="6.42578" y="329.427" width="107.938" height="102.091" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter14_d_601_3962" x="52.9844" y="337.818" width="12.9062" height="28.1151" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter15_d_601_3962" x="42.6484" y="379.218" width="29.9141" height="15.778" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter16_d_601_3962" x="82.6719" y="338.187" width="16.4414" height="13.6509" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter17_d_601_3962" x="4.99609" y="385.696" width="144.172" height="72.2034" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter18_d_601_3962" x="5.64844" y="400.741" width="144.008" height="41.2189" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter19_d_601_3962" x="85.2109" y="386.362" width="20.25" height="24.5795" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter20_d_601_3962" x="56.5508" y="416.493" width="29.6992" height="16.4171" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter21_d_601_3962" x="117.133" y="402.562" width="12.3516" height="17.1344" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter22_d_601_3962" x="651.441" y="465.762" width="73.043" height="130" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter23_d_601_3962" x="651.059" y="465.183" width="70.957" height="129.917" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter24_d_601_3962" x="687.512" y="489.585" width="16.8164" height="26.8513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter25_d_601_3962" x="673.68" y="526.163" width="26.4922" height="22.325" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter26_d_601_3962" x="660.969" y="481.048" width="17.5195" height="11.2894" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter27_d_601_3962" x="591.469" y="519.849" width="131.207" height="88.5939" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter28_d_601_3962" x="591.039" y="528.264" width="131.195" height="68.5201" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter29_d_601_3962" x="640.293" y="521.828" width="15.6875" height="27.3905" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter30_d_601_3962" x="645.832" y="563.492" width="30.9375" height="11.2268" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter31_d_601_3962" x="511.137" y="573.794" width="27.0938" height="27.092" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter32_d_601_3962" x="609.555" y="530.074" width="14.7812" height="15.6245" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter33_d_601_3962" x="544.949" y="411.204" width="27.0938" height="27.092" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter34_d_601_3962" x="367.32" y="146.416" width="27.0938" height="27.092" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter35_d_601_3962" x="176.75" y="407.129" width="223.469" height="223.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter36_d_601_3962" x="278.473" y="445.108" width="98" height="22.487" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter37_d_601_3962" x="241.934" y="462.294" width="8.92969" height="8.74054" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter38_d_601_3962" x="190.609" y="462.587" width="59.3242" height="15.5545" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter39_d_601_3962" x="234.051" y="570.669" width="91.0898" height="59.9204" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter40_d_601_3962" x="246.934" y="503.235" width="69.75" height="69.7511" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter41_d_601_3962" x="242.984" y="491.296" width="73.1484" height="82.4682" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter42_d_601_3962" x="285.484" y="296.536" width="239.559" height="98.3203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter43_d_601_3962" x="284.484" y="295.536" width="241.559" height="100.32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter44_d_601_3962" x="310.645" y="321.667" width="193.059" height="13.1816" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter45_d_601_3962" x="310.645" y="332.673" width="192.73" height="13.1816" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter46_d_601_3962" x="310.645" y="343.68" width="192.551" height="13.1816" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter47_d_601_3962" x="560.914" y="178.416" width="223.469" height="223.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter48_d_601_3962" x="584.742" y="209.108" width="98" height="22.4869" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter49_d_601_3962" x="710.348" y="226.294" width="8.92969" height="8.74048" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter50_d_601_3962" x="711.277" y="226.587" width="59.3242" height="15.5545" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter51_d_601_3962" x="624.141" y="267.128" width="72.7188" height="74.1592" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter52_d_601_3962" x="635.996" y="341.955" width="91.0898" height="59.9204" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter53_d_601_3962" x="617.164" y="251.274" width="169.48" height="93.1743" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter54_d_601_3962" x="78.8242" y="111.416" width="223.469" height="223.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter55_d_601_3962" x="180.473" y="142.108" width="98" height="22.4869" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter56_d_601_3962" x="143.934" y="159.294" width="8.92969" height="8.74048" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter57_d_601_3962" x="92.6094" y="159.587" width="59.3242" height="15.5545" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter58_d_601_3962" x="136.125" y="274.955" width="91.0898" height="59.9204" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter59_d_601_3962" x="158.68" y="192.976" width="80.1445" height="80.1463" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<filter id="filter60_d_601_3962" x="145.887" y="183.709" width="109.539" height="88.3001" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_601_3962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_601_3962" result="shape"/>
</filter>
<linearGradient id="paint0_linear_601_3962" x1="279.596" y1="570.669" x2="279.596" y2="622.589" gradientUnits="userSpaceOnUse">
<stop stop-color="#DC0916"/>
<stop offset="1" stop-color="#F41FD6"/>
</linearGradient>
<linearGradient id="paint1_linear_601_3962" x1="681.541" y1="341.955" x2="681.541" y2="393.876" gradientUnits="userSpaceOnUse">
<stop stop-color="#DC0916"/>
<stop offset="1" stop-color="#F41FD6"/>
</linearGradient>
<linearGradient id="paint2_linear_601_3962" x1="181.67" y1="274.955" x2="181.67" y2="326.876" gradientUnits="userSpaceOnUse">
<stop stop-color="#DC0916"/>
<stop offset="1" stop-color="#F41FD6"/>
</linearGradient>
<clipPath id="clip0_601_3962">
<rect width="809.342" height="629.876" fill="white"/>
</clipPath>
</defs>
</svg>

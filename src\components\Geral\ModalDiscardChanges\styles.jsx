import styled from "styled-components";

export const Container = styled.div`
  width: 430px;
  height: 320px;
  position: absolute;
  background-color: rgba(245, 247, 250, 1);
  border-left: 19px solid #007bff;
  border-radius: 8px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);
  display: grid;
  margin-left: 850px;
  margin-top: -820px;
`;
export const SpanTitle = styled.span`
  font-family: "Houschka Rounded Alt";
  font-style: normal;
  font-size: 30px;
  padding-left: 44px;
  text-align: center;
`;
export const PositionButtonSave = styled.div`
  margin-left: 80px;
  margin-top: 20px;
`;
export const PositionButtonCancel = styled.div`
  margin-left: 80px;
  margin-bottom: 5px;
`;

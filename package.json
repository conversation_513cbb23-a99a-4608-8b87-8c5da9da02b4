{"name": "spa", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tippyjs/react": "^4.2.6", "axios": "^1.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-google-charts": "^4.0.0", "react-helmet": "^6.1.0", "react-icons": "^4.7.1", "react-router-dom": "^6.9.0", "react-scripts": "5.0.1", "react-select": "^5.7.0", "react-tippy": "^1.4.0", "styled-components": "^5.3.8", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
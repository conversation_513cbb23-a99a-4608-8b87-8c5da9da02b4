import styled, { css } from "styled-components";

// Div Father
export const ContainerCentral = styled.div`
  justify-content: end;
  display: flex;
  grid-row: 1;
  grid-column: 2;
  z-index: 2000;
  @media (min-width: 100px) and (max-width: 590px) {
    grid-column: 1;
    grid-row: 1;
    display: grid;
    position: absolute;
    top: 0px;
    left: 0;
    justify-content: start;
  }
  transform: ${(props) =>
    props.$mode ? "translate(-7%, -8%)" : "translate(0%, 1%)"};
`;

// Div Title
export const PositionTitle = styled.div`
  margin-left: 0px;
  height: 100%;
  grid-row: 1;
  display: grid;
  grid-template-columns: 80% 20%;
  @media (min-width: 50px) and (max-width: 500px) {
    display: grid;
    grid-template-columns: 70% 10%;
  }
`;

export const H1 = styled.h1`
  font-size: 32px;
  font-style: normal;
  font-weight: 600;
  line-height: 36px;
  display: flex;
  height: 100%;
  align-items: center;
  width: 100%;
  padding-left: 9%;
  padding-top: 2%;
  grid-column: 1;
  @media (min-width: 50px) and (max-width: 500px) {
    font-size: 1.5rem;
  }
`;

/// Button Close

export const DivClose = styled.div`
  align-items: center;
  display: grid;
  width: 50%;
  @media (min-width: 50px) and (max-width: 500px) {
  }
`;

export const Close = styled.button`
  background-color: #2b2b2b;
  color: #fff;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: grid;
  width: 100%;
  height: 20px;
  :hover {
    opacity: 0.8;
  }
`;

// Children

export const Container = styled.div`
  grid-row: 1;
  grid-column: 2;
  transform: ${(props) => (props.$mode ? "translate(-50%, 10%)" : "")};
  width: ${(props) => (props.$mode ? "40%" : "75%")};
  min-width: 350px;
  height: ${(props) => (props.$mode ? "700px" : "100%")};
  background: #fff;
  display: grid;
  border-left: 16.56px solid #007bff;
  border-radius: 8px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);
  z-index: 1000;
  grid-template-rows: 10% 75% 15%;
  @media (width: 500px) {
    height: 81vh;
    width: 375px;
    flex-direction: flex-start;
    position: relative;
  }
  @media (min-width: 1355px) and (max-width: 1824px) {
    width: 80%;
  }
`;

// Div Content

export const Form = styled.div`
  position: relative;
  display: grid;
  height: 100%;
  width: 93%;
  padding-left: 7%;
  grid-row: 2;
  grid-template-rows: 14.2% 14.2% 14.2% 14.2% 14.2% 29%;
  @media (min-width: 1355px) and (max-width: 1824px) {
    width: 95%;
    height: 100%;
  }
  @media (min-width: 1824px) and (max-width: 2000px) {
    width: 95%;
  }
`;

export const Input = styled.input`
  height: ${(props) => props.heightInput || "28px"};
  width: ${(props) => props.widthInput || "100%"};
  border-radius: 5px;
  border: 2px solid #888c95;
  background-color: ${(props) => props.backgroundInput || " rgba(0, 0, 0, 0) "};
  display: block;
  position: relative;
  padding-left: 2%;
  border: 2px solid
    ${(props) => (props.placeholder === "" ? "#888C95" : "#b03535")};
  @media (min-width: 1355px) and (max-width: 1824px) {
    height: 25px;
  }
`;

export const InputDisable = styled.input`
  height: ${(props) => props.heightInput || "28px"};
  width: ${(props) => props.widthInput || "100%"};
  border-radius: 5px;
  border: 2px solid #888c95;
  background-color: #d6d6d6;
  display: block;
  position: relative;
  padding-left: 2%;
  @media (min-width: 1355px) and (max-width: 1824px) {
    height: 25px;
  }
`;

export const TextArea = styled.textarea`
  height: ${(props) => props.heightInput || "80px"};
  width: ${(props) => props.widthInput || "100%"};
  border-radius: 5px;
  border: 2px solid #888c95;
  background-color: ${(props) => props.backgroundInput || " rgba(0, 0, 0, 0) "};
  border: 2px solid
    ${(props) => (props.placeholder === "" ? "#888C95" : "#b03535")};
  display: block;
  resize: none;
  padding-left: 2%;
  position: relative;
  @media (min-width: 1355px) and (max-width: 1824px) {
    height: 70px;
  }
  @media (min-width: 1824px) and (max-width: 2000px) {
    height: 100px;
  }
`;

export const Label = styled.label`
  padding-left: 0px;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.8);
`;

// Divs

export const DivName = styled.div`
  grid-row: 1;
  width: 90%;
  height: 100%;
  display: grid;
`;

export const DivBusiness = styled.div`
  grid-row: 2;
  grid-template-columns: 50% 50%;
  width: 90%;
  height: 100%;
  display: grid;
`;

export const DivEmail = styled.div`
  width: 90%;
  height: 100%;
  display: grid;
  grid-row: 3;
`;

export const DivSubject = styled.div`
  grid-row: 4;
  width: 90%;
  height: auto;
  display: grid;
`;

export const DivStatus = styled.div`
  grid-row: 5;
  grid-template-columns: 50% 50%;
  width: 90%;
  height: 100%;
  display: grid;
`;

export const DivDescription = styled.div`
  grid-row: 5;
  width: 90%;
  height: auto;
  display: grid;
`;

// Button Save and Cancel

export const DivButton = styled.div`
  justify-content: center;
  display: grid;
  position: relative;
  align-items: center;
  width: 100%;
  height: 100%;
  grid-row: 3;
  @media (min-width: 1355px) and (max-width: 1824px) {
    bottom: 10%;
    position: relative;
    height: 100%;
  }
  @media (min-width: 1824px) and (max-width: 2000px) {
    height: 85%;
  }
`;

export const PositionButtonCancel = styled.div``;

export const ClickButton = styled.div`
  display: block;
`;

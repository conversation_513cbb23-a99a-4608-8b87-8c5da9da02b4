import styled from "styled-components";

export const DivForm = styled.div`
  border-left: 20px solid rgba(228, 17, 101, 1);
  background: rgba(245, 247, 250, 1);
  box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  width: 544px;
  height: 772px;
  display: grid;
  grid-template-columns: 100%;
  align-items: left;
  padding: 30px;
  gap: 10px;
  position: absolute;
  left: 3.37%;
  right: 0%;
  top: 0%;
  bottom: 0%;
`;
export const Title = styled.div`
  padding-bottom: 1%;
`;

export const DivUser = styled.div`
  padding-bottom: 1%;
  padding-top: 1px;
`;

export const DivCenter = styled.div`
  display: grid;
  grid-template-columns: 50% 50%;
  padding-bottom: 1px;
`;

export const DivStatus = styled.div`
  padding-bottom: 1%;
  width: 100%;
`;

export const DivEmail = styled.div`
  width: 100%;
`;

export const DivLevel = styled.div`
  width: 100%;
`;

export const Button1 = styled.div`
  padding-top: 1%;
  padding-bottom: 1%;
  margin-left: 100px;
`;
export const Button2 = styled.div`
  margin-left: 100px;
`;

export const DivDouble = styled.div`
  margin-left: 100px;
`;
